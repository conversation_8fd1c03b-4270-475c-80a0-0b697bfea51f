# rms_fx_gytj 存储过程测试用例

## 存储过程功能概述

`rms_fx_gytj` 是一个给药途径分析存储过程，主要功能包括：
- 验证药品的给药途径是否符合标准
- 检查中药的煎药方式是否正确
- 验证西药的给药途径是否在说明书范围内
- 对不符合要求的给药途径生成错误提示信息

## 输入参数

- `p_Code`: 处方编码 (VARCHAR(50))
- `p_akb020`: 医保编码 (VARCHAR(20))
- `p_yp_code`: 药品编码 (VARCHAR(20))
- `p_yp_tj`: 给药途径编码 (VARCHAR(20))

## 测试环境准备

### 基础测试数据

```sql
-- 清理测试数据
DELETE FROM rms_t_pres_fx WHERE code LIKE 'TEST_%';
DELETE FROM rms_itf_hos_drug WHERE drug_code LIKE 'TEST_%';
DELETE FROM rms_t_sda WHERE id >= 90000;
DELETE FROM rms_t_byyydzb WHERE yp_code LIKE 'TEST_%';
DELETE FROM rms_t_sda_gytj WHERE sda_id >= 90000;
DELETE FROM rms_t_tjdzb WHERE h_tj LIKE 'TEST_%';
DELETE FROM rms_t_pres_med WHERE code LIKE 'TEST_%';
DELETE FROM rms_t_med_zdy_gytj WHERE yp_code LIKE 'TEST_%';

-- 插入测试用标准数据
INSERT INTO rms_t_sda (id, ym, gytj_bs) VALUES 
(90001, '阿司匹林片', '0'),
(90002, '甘草', '1'),
(90003, '胰岛素注射液', '0'),
(90004, '醋酸戈舍瑞林缓释植入剂', '0'),
(90005, '复方甘草片', '0');

-- 插入测试用药品数据
INSERT INTO rms_itf_hos_drug (drug_code, drug_name, zx_flag, is_rm, drug_for_name) VALUES
('TEST_001', '阿司匹林片', '1', '0', '片剂'),
('TEST_002', '甘草', '3', '0', '饮片'),
('TEST_003', '甘草颗粒', '3', '0', '颗粒剂'),
('TEST_004', '胰岛素注射液', '1', '0', '注射剂'),
('TEST_005', '生理盐水', '1', '1', '注射剂'),
('TEST_006', '醋酸戈舍瑞林缓释植入剂', '1', '0', '植入剂'),
('TEST_007', '复方甘草片', '1', '0', '片剂');

-- 插入药品标准数据关联
INSERT INTO rms_t_byyydzb (akb020, yp_code, sda_id) VALUES
('A001', 'TEST_001', 90001),
('A002', 'TEST_002', 90002),
('A003', 'TEST_004', 90003),
('A004', 'TEST_006', 90004),
('A005', 'TEST_007', 90005);

-- 插入给药途径对照数据
INSERT INTO rms_t_tjdzb (akb020, h_tj, h_tname, by_code, by_name) VALUES
('A001', 'TEST_PO', '口服', '0101', '口服'),
('A001', 'TEST_IV', '静脉注射', '0201', '静脉注射'),
('A002', 'TEST_DECOCT', '煎服', 'ZY01', '煎服'),
('A003', 'TEST_SC', '皮下注射', '0202', '皮下注射'),
('A004', 'TEST_IMPL', '植入', '0301', '植入');

-- 插入标准给药途径数据
INSERT INTO rms_t_sda_gytj (sda_id, gytj_code, bs) VALUES
(90001, '0101', '0'),  -- 阿司匹林片-口服
(90002, 'ZY01', '0'),  -- 甘草-煎服
(90003, '0202', '0'),  -- 胰岛素-皮下注射
(90004, '0301', '0'),  -- 戈舍瑞林-植入
(90005, '0101', '0');  -- 复方甘草片-口服
```

## 测试用例

### 1. 正常情况测试（正向测试用例）

#### 测试用例1.1: 西药正确给药途径
**测试描述**: 验证西药使用正确给药途径时不产生错误提示

**前置条件**: 
- 药品为西药（zx_flag='1'）
- 给药途径在标准范围内
- 非溶媒药品

**输入参数**:
```sql
CALL rms_fx_gytj('TEST_PRES_001', 'A001', 'TEST_001', 'TEST_PO');
```

**预期结果**: 
- 不在rms_t_pres_fx表中插入错误记录
- 存储过程正常执行完成

**测试步骤**:
```sql
-- 执行存储过程
CALL rms_fx_gytj('TEST_PRES_001', 'A001', 'TEST_001', 'TEST_PO');

-- 验证结果
SELECT COUNT(*) as error_count 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001';
-- 期望结果: error_count = 0
```

#### 测试用例1.2: 中药正确煎药方式
**测试描述**: 验证中药使用正确煎药方式时不产生错误提示

**前置条件**:
- 药品为中药（zx_flag='3'）
- 给药途径标识为1（需要验证煎药方式）
- 煎药方式在标准范围内

**输入参数**:
```sql
CALL rms_fx_gytj('TEST_PRES_002', 'A002', 'TEST_002', 'TEST_DECOCT');
```

**预期结果**: 不产生错误提示

**测试步骤**:
```sql
CALL rms_fx_gytj('TEST_PRES_002', 'A002', 'TEST_002', 'TEST_DECOCT');

SELECT COUNT(*) as error_count 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002';
-- 期望结果: error_count = 0
```

### 2. 边界条件测试

#### 测试用例2.1: 空给药途径参数
**测试描述**: 验证给药途径参数为空时的处理

**输入参数**:
```sql
CALL rms_fx_gytj('TEST_PRES_003', 'A001', 'TEST_001', '');
```

**预期结果**: 
- 西药给药途径为空时应该直接返回，不产生错误
- 中药给药途径为空且需要验证时应产生错误提示

#### 测试用例2.2: 不存在的药品编码
**测试描述**: 验证药品编码不存在时的处理

**输入参数**:
```sql
CALL rms_fx_gytj('TEST_PRES_004', 'A999', 'NONEXIST_001', 'TEST_PO');
```

**预期结果**: 存储过程应正常执行，不产生异常

### 3. 异常情况测试（负向测试用例）

#### 测试用例3.1: 中药煎药方式为空
**测试描述**: 验证中药需要煎药方式但参数为空时的错误提示

**前置条件**:
- 药品为中药（zx_flag='3'）
- 给药途径标识为1（需要验证）
- 给药途径参数为空

**输入参数**:
```sql
CALL rms_fx_gytj('TEST_PRES_005', 'A002', 'TEST_002', '');
```

**预期结果**: 
- 在rms_t_pres_fx表中插入错误记录
- 错误信息包含"煎药方式不能为空"

**测试步骤**:
```sql
CALL rms_fx_gytj('TEST_PRES_005', 'A002', 'TEST_002', '');

SELECT wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_005';
-- 期望结果: wtname='给药途径错误', detail包含'煎药方式不能为空'
```

#### 测试用例3.2: 中药不支持的煎药方式
**测试描述**: 验证中药使用不支持的煎药方式时的错误提示

**输入参数**:
```sql
CALL rms_fx_gytj('TEST_PRES_006', 'A002', 'TEST_002', 'INVALID_METHOD');
```

**预期结果**: 
- 产生"中国药典2020版未提及该煎药方法"错误提示

#### 测试用例3.3: 西药错误给药途径
**测试描述**: 验证西药使用错误给药途径时的错误提示

**输入参数**:
```sql
CALL rms_fx_gytj('TEST_PRES_007', 'A001', 'TEST_001', 'TEST_IV');
```

**预期结果**: 
- 产生"说明书未提及该给药途径"错误提示

#### 测试用例3.4: 溶媒药品检查
**测试描述**: 验证溶媒药品不进行给药途径检查

**输入参数**:
```sql
CALL rms_fx_gytj('TEST_PRES_008', 'A001', 'TEST_005', 'TEST_PO');
```

**预期结果**: 
- 不产生任何错误提示（溶媒药品跳过检查）

#### 测试用例3.5: 中药颗粒剂检查
**测试描述**: 验证中药颗粒剂不进行给药途径检查

**输入参数**:
```sql
CALL rms_fx_gytj('TEST_PRES_009', 'A002', 'TEST_003', 'TEST_DECOCT');
```

**预期结果**:
- 不产生任何错误提示（中药颗粒剂跳过检查）

### 4. 输入参数验证测试

#### 测试用例4.1: NULL参数处理
**测试描述**: 验证输入参数为NULL时的处理

**输入参数**:
```sql
CALL rms_fx_gytj(NULL, 'A001', 'TEST_001', 'TEST_PO');
CALL rms_fx_gytj('TEST_PRES_010', NULL, 'TEST_001', 'TEST_PO');
CALL rms_fx_gytj('TEST_PRES_011', 'A001', NULL, 'TEST_PO');
CALL rms_fx_gytj('TEST_PRES_012', 'A001', 'TEST_001', NULL);
```

**预期结果**:
- 存储过程应正常执行，不产生异常
- 对于关键参数为NULL的情况，应提前返回

#### 测试用例4.2: 超长参数处理
**测试描述**: 验证超长参数的处理

**输入参数**:
```sql
CALL rms_fx_gytj('TEST_PRES_' + REPEAT('X', 100), 'A001', 'TEST_001', 'TEST_PO');
```

**预期结果**:
- 存储过程应正常执行或产生合理的错误提示

### 5. 特殊药品类型测试

#### 测试用例5.1: 胰岛素类药品皮下注射检查
**测试描述**: 验证胰岛素类药品使用口服途径时的错误提示

**前置条件**:
- 药品名称包含"胰岛"
- 使用口服给药途径

**输入参数**:
```sql
CALL rms_fx_gytj('TEST_PRES_013', 'A003', 'TEST_004', 'TEST_PO');
```

**预期结果**:
- 产生"说明书未提及该给药途径"错误提示

#### 测试用例5.2: 醋酸戈舍瑞林特殊处理
**测试描述**: 验证醋酸戈舍瑞林药品的特殊处理逻辑

**输入参数**:
```sql
CALL rms_fx_gytj('TEST_PRES_014', 'A004', 'TEST_006', 'TEST_IV');
```

**预期结果**:
- 不产生错误提示（醋酸戈舍瑞林有特殊处理逻辑）

#### 测试用例5.3: 非注射剂使用注射途径
**测试描述**: 验证非注射剂型使用注射给药途径时的错误提示

**前置条件**:
- 药品剂型不包含"注射"或"针"
- 使用注射给药途径

**输入参数**:
```sql
CALL rms_fx_gytj('TEST_PRES_015', 'A005', 'TEST_007', 'TEST_IV');
```

**预期结果**:
- 产生"说明书未提及该给药途径"错误提示

### 6. 中药特殊用法检查测试

#### 测试用例6.1: 中药足浴用法
**测试描述**: 验证中药足浴用法不进行给药途径检查

**前置条件**:
- 在rms_t_pres_med表中存在用药说明为"足浴"的记录

**测试步骤**:
```sql
-- 插入处方药品记录
INSERT INTO rms_t_pres_med (code, his_code, yysm)
VALUES ('TEST_PRES_016', 'TEST_002', '足浴');

-- 执行存储过程
CALL rms_fx_gytj('TEST_PRES_016', 'A002', 'TEST_002', 'INVALID_METHOD');

-- 验证结果
SELECT COUNT(*) as error_count
FROM rms_t_pres_fx
WHERE code = 'TEST_PRES_016';
-- 期望结果: error_count = 0（足浴用法跳过检查）
```

#### 测试用例6.2: 中药外用用法
**测试描述**: 验证中药外用用法不进行给药途径检查

**测试步骤**:
```sql
INSERT INTO rms_t_pres_med (code, his_code, yysm)
VALUES ('TEST_PRES_017', 'TEST_002', '外用');

CALL rms_fx_gytj('TEST_PRES_017', 'A002', 'TEST_002', 'INVALID_METHOD');

SELECT COUNT(*) as error_count
FROM rms_t_pres_fx
WHERE code = 'TEST_PRES_017';
-- 期望结果: error_count = 0
```

#### 测试用例6.3: 中药打粉用法
**测试描述**: 验证中药打粉用法不进行给药途径检查

**测试步骤**:
```sql
INSERT INTO rms_t_pres_med (code, his_code, yysm)
VALUES ('TEST_PRES_018', 'TEST_002', '打粉');

CALL rms_fx_gytj('TEST_PRES_018', 'A002', 'TEST_002', 'INVALID_METHOD');

SELECT COUNT(*) as error_count
FROM rms_t_pres_fx
WHERE code = 'TEST_PRES_018';
-- 期望结果: error_count = 0
```

### 7. 自定义给药途径测试

#### 测试用例7.1: 自定义给药途径优先级
**测试描述**: 验证自定义给药途径的优先级高于标准给药途径

**前置条件**:
- 在rms_t_med_zdy_gytj表中存在自定义给药途径

**测试步骤**:
```sql
-- 插入自定义给药途径
INSERT INTO rms_t_med_zdy_gytj (yp_code, gytj_code)
VALUES ('TEST_001', 'CUSTOM_ROUTE');

-- 执行存储过程（使用自定义途径）
CALL rms_fx_gytj('TEST_PRES_019', 'A001', 'TEST_001', 'CUSTOM_ROUTE');

-- 验证结果
SELECT COUNT(*) as error_count
FROM rms_t_pres_fx
WHERE code = 'TEST_PRES_019';
-- 期望结果: error_count = 0（自定义途径不产生错误）
```

### 8. 输出结果验证测试

#### 测试用例8.1: 错误信息格式验证
**测试描述**: 验证错误信息的格式和内容正确性

**测试步骤**:
```sql
-- 触发中药煎药方式错误
CALL rms_fx_gytj('TEST_PRES_020', 'A002', 'TEST_002', '');

-- 验证错误信息格式
SELECT
    wtlvlcode,
    wtlvl,
    wtcode,
    wtsp,
    wtname,
    title,
    detail,
    flag,
    text
FROM rms_t_pres_fx
WHERE code = 'TEST_PRES_020';

-- 期望结果验证:
-- wtlvlcode = '1'
-- wtlvl = '一般提示'
-- wtcode = 'RLT003'
-- wtsp = 'GYTJJJ'
-- wtname = '给药途径错误'
-- title = '草药煎药方法错误'
-- detail 包含药品名称和建议煎药方法
-- flag = '0'
-- text = '给药途径错误'
```

#### 测试用例8.2: 多个错误情况处理
**测试描述**: 验证一个处方中多个药品都有给药途径错误时的处理

**测试步骤**:
```sql
-- 执行多个有错误的给药途径检查
CALL rms_fx_gytj('TEST_PRES_021', 'A002', 'TEST_002', '');
CALL rms_fx_gytj('TEST_PRES_021', 'A001', 'TEST_001', 'TEST_IV');

-- 验证结果
SELECT COUNT(*) as error_count,
       GROUP_CONCAT(DISTINCT ywa) as error_drugs
FROM rms_t_pres_fx
WHERE code = 'TEST_PRES_021';
-- 期望结果: error_count = 2, error_drugs包含两个药品名称
```

## 完整测试执行脚本

```sql
-- 执行所有测试用例的完整脚本
-- 1. 准备测试环境
-- （前面的基础测试数据插入语句）

-- 2. 执行所有测试用例
-- 正向测试
CALL rms_fx_gytj('TEST_PRES_001', 'A001', 'TEST_001', 'TEST_PO');
CALL rms_fx_gytj('TEST_PRES_002', 'A002', 'TEST_002', 'TEST_DECOCT');

-- 负向测试
CALL rms_fx_gytj('TEST_PRES_005', 'A002', 'TEST_002', '');
CALL rms_fx_gytj('TEST_PRES_006', 'A002', 'TEST_002', 'INVALID_METHOD');
CALL rms_fx_gytj('TEST_PRES_007', 'A001', 'TEST_001', 'TEST_IV');

-- 特殊情况测试
CALL rms_fx_gytj('TEST_PRES_008', 'A001', 'TEST_005', 'TEST_PO');
CALL rms_fx_gytj('TEST_PRES_009', 'A002', 'TEST_003', 'TEST_DECOCT');

-- 3. 验证测试结果
SELECT
    code,
    ywa,
    wtname,
    title,
    detail,
    CASE
        WHEN code IN ('TEST_PRES_001', 'TEST_PRES_002', 'TEST_PRES_008', 'TEST_PRES_009')
        THEN '应该无错误'
        ELSE '应该有错误'
    END as expected_result
FROM rms_t_pres_fx
WHERE code LIKE 'TEST_PRES_%'
ORDER BY code;

-- 4. 清理测试数据
DELETE FROM rms_t_pres_fx WHERE code LIKE 'TEST_%';
DELETE FROM rms_t_pres_med WHERE code LIKE 'TEST_%';
DELETE FROM rms_t_med_zdy_gytj WHERE yp_code LIKE 'TEST_%';
DELETE FROM rms_itf_hos_drug WHERE drug_code LIKE 'TEST_%';
DELETE FROM rms_t_byyydzb WHERE yp_code LIKE 'TEST_%';
DELETE FROM rms_t_sda_gytj WHERE sda_id >= 90000;
DELETE FROM rms_t_tjdzb WHERE h_tj LIKE 'TEST_%';
DELETE FROM rms_t_sda WHERE id >= 90000;
```

## 测试覆盖率分析

本测试用例集覆盖了以下场景：
1. ✅ 正常给药途径验证（西药、中药）
2. ✅ 异常给药途径检测
3. ✅ 特殊药品类型处理（溶媒、中药颗粒、胰岛素等）
4. ✅ 中药特殊用法检查（足浴、外用、打粉）
5. ✅ 自定义给药途径优先级
6. ✅ 输入参数验证
7. ✅ 错误信息格式验证
8. ✅ 边界条件处理
9. ✅ NULL值处理
10. ✅ 异常处理机制

## 注意事项

1. 测试前请确保数据库中存在必要的基础数据
2. 测试用例使用TEST_前缀，便于识别和清理
3. 每个测试用例都应该独立执行，不依赖其他测试用例的结果
4. 建议在测试环境中执行，避免影响生产数据
5. 测试完成后及时清理测试数据
